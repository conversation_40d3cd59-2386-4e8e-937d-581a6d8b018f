#include <iostream>
#include <filesystem>
#include <fstream>
#include <string>

int main() {
    std::string app_name = "notepad2";
    std::filesystem::path app_dir = "C:\\Users\\<USER>\\scoop\\apps\\" + app_name;
    
    std::cout << "Checking version for: " << app_name << std::endl;
    std::cout << "App directory: " << app_dir << std::endl;
    
    if (!std::filesystem::exists(app_dir)) {
        std::cout << "App directory does not exist!" << std::endl;
        return 1;
    }
    
    // Step 1: Try to read from current\manifest.json
    auto current_manifest = app_dir / "current" / "manifest.json";
    std::cout << "Checking: " << current_manifest << std::endl;
    if (std::filesystem::exists(current_manifest)) {
        std::cout << "manifest.json exists!" << std::endl;
        try {
            std::ifstream file(current_manifest);
            if (file.is_open()) {
                std::string content((std::istreambuf_iterator<char>(file)),
                                   std::istreambuf_iterator<char>());
                std::cout << "Content: " << content << std::endl;
                
                // Simple version extraction (not using nlohmann::json)
                size_t pos = content.find("\"version\":");
                if (pos != std::string::npos) {
                    size_t start = content.find("\"", pos + 10);
                    if (start != std::string::npos) {
                        size_t end = content.find("\"", start + 1);
                        if (end != std::string::npos) {
                            std::string version = content.substr(start + 1, end - start - 1);
                            std::cout << "Found version: " << version << std::endl;
                            return 0;
                        }
                    }
                }
            } else {
                std::cout << "Failed to open manifest.json" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "Exception reading manifest.json: " << e.what() << std::endl;
        }
    } else {
        std::cout << "manifest.json does not exist" << std::endl;
    }
    
    // Step 2: Try to read from current\install.json
    auto current_install = app_dir / "current" / "install.json";
    std::cout << "Checking: " << current_install << std::endl;
    if (std::filesystem::exists(current_install)) {
        std::cout << "install.json exists!" << std::endl;
        try {
            std::ifstream file(current_install);
            if (file.is_open()) {
                std::string content((std::istreambuf_iterator<char>(file)),
                                   std::istreambuf_iterator<char>());
                std::cout << "Content: " << content << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "Exception reading install.json: " << e.what() << std::endl;
        }
    } else {
        std::cout << "install.json does not exist" << std::endl;
    }
    
    // Step 3: Check version directories
    std::cout << "Checking version directories:" << std::endl;
    try {
        for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
            if (entry.is_directory()) {
                std::string dir_name = entry.path().filename().string();
                std::cout << "Found directory: " << dir_name << std::endl;
                
                if (dir_name != "current" && dir_name != "persist") {
                    auto install_json = entry.path() / "install.json";
                    if (std::filesystem::exists(install_json)) {
                        std::cout << "Version directory " << dir_name << " has install.json" << std::endl;
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        std::cout << "Exception checking directories: " << e.what() << std::endl;
    }
    
    return 0;
}

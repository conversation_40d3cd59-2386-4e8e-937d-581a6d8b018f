#pragma once

#include <string>
#include <vector>
#include <filesystem>
#include <fstream>
#include <chrono>
#include <sstream>
#include <algorithm>
#include <iostream>
#include <thread>
#include <ctime>
#include <nlohmann/json.hpp>
#include "config.hpp"
#include "manifest.hpp"
#include "dependency_resolver.hpp"
#include "../utils/shim_manager.hpp"
#include "../utils/output.hpp"

namespace sco {

class UninstallManager {
public:
    struct UninstallOptions {
        bool global = false;
        bool purge = false;  // Remove all data including persist directory
        bool force = false;  // Force uninstall even if other apps depend on it
    };
    
    struct UninstallResult {
        bool success = false;
        std::string error_message;
        std::vector<std::string> uninstalled_apps;
        std::vector<std::string> failed_apps;
        std::vector<std::string> skipped_apps;  // Apps that have dependents
        std::chrono::milliseconds total_duration{0};
    };
    
    static UninstallResult uninstall_apps(const std::vector<std::string>& app_names, 
                                         const UninstallOptions& options = {}) {
        UninstallManager manager(options);
        return manager.perform_uninstallation(app_names);
    }
    
    // Check what apps depend on the given apps
    static std::vector<std::string> get_dependents(const std::vector<std::string>& app_names) {
        std::vector<std::string> dependents;
        auto installed_apps = get_installed_apps();
        
        for (const auto& installed_app : installed_apps) {
            auto manifest = get_installed_manifest(installed_app);
            if (!manifest.is_valid()) continue;
            
            for (const auto& dep : manifest.depends) {
                std::string dep_name = extract_app_name(dep);
                if (std::find(app_names.begin(), app_names.end(), dep_name) != app_names.end()) {
                    if (std::find(dependents.begin(), dependents.end(), installed_app) == dependents.end()) {
                        dependents.push_back(installed_app);
                    }
                }
            }
        }
        
        return dependents;
    }
    
private:
    UninstallOptions options_;
    Config& config_;
    
    explicit UninstallManager(const UninstallOptions& options) 
        : options_(options), config_(Config::instance()) {
        config_.load();
        if (options_.global) {
            config_.set_global_mode(true);
        }
    }
    
    UninstallResult perform_uninstallation(const std::vector<std::string>& app_names) {
        UninstallResult result;
        auto start_time = std::chrono::steady_clock::now();
        
        std::cout << "Starting uninstallation of " << app_names.size() << " app(s)...\n";
        
        try {
            // Step 1: Validate that all apps are installed
            std::vector<std::string> valid_apps;
            for (const auto& app_name : app_names) {
                if (is_app_installed(app_name)) {
                    valid_apps.push_back(app_name);
                } else {
                    output::warn("App " + app_name + " is not installed, skipping");
                    result.skipped_apps.push_back(app_name);
                }
            }
            
            if (valid_apps.empty()) {
                std::cout << "No valid apps to uninstall.\n";
                result.success = true;
                return result;
            }
            
            // Step 2: Check for dependents (unless force)
            if (!options_.force) {
                auto dependents = get_dependents(valid_apps);
                if (!dependents.empty()) {
                    result.error_message = "Cannot uninstall apps that other apps depend on. Use --force to override.";
                    result.skipped_apps.insert(result.skipped_apps.end(), dependents.begin(), dependents.end());
                    
                    output::error("Apps have dependents: " + join_strings(dependents, ", "));
                    return result;
                }
            }
            
            // Step 3: Determine uninstall order (reverse dependency order)
            std::vector<std::string> uninstall_order = determine_uninstall_order(valid_apps);
            
            std::cout << "Uninstalling " << uninstall_order.size() << " app(s) in order: "
                      << join_strings(uninstall_order, ", ") << "\n";
            
            // Step 4: Uninstall each app in order
            for (const auto& app_name : uninstall_order) {
                if (uninstall_single_app(app_name)) {
                    result.uninstalled_apps.push_back(app_name);
                    output::success("Successfully uninstalled: " + app_name);
                } else {
                    result.failed_apps.push_back(app_name);
                    std::cout << "✗ Failed to uninstall: " << app_name << "\n";
                }
            }
            
            result.success = result.failed_apps.empty();
            
        } catch (const std::exception& e) {
            result.error_message = e.what();
            output::error("Uninstallation failed with exception: " + std::string(e.what()));
        }
        
        auto end_time = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        output::debug("Uninstallation completed in " + std::to_string(result.total_duration.count()) +
                     "ms. Success: " + (result.success ? "true" : "false") +
                     ", Uninstalled: " + std::to_string(result.uninstalled_apps.size()) +
                     ", Failed: " + std::to_string(result.failed_apps.size()));
        
        return result;
    }
    
    bool uninstall_single_app(const std::string& app_name) {
        try {
            // Step 1: Get current version and directories (Scoop's Select-CurrentVersion)
            auto version = get_current_version(app_name);
            if (version.empty()) {
                output::error("Could not determine current version for: {}", app_name);
                return false;
            }

            auto app_dir = get_app_directory(app_name);
            auto version_dir = app_dir / version;
            auto persist_dir = get_persist_directory(app_name);

            std::cout << "Uninstalling '" << app_name << "' (" << version << ").\n";

            if (!std::filesystem::exists(version_dir)) {
                output::error("Version directory not found: {}", version_dir.string());
                return false;
            }

            auto manifest = get_installed_manifest(app_name);
            auto install_info = get_install_info(app_name, version);
            std::string architecture = install_info.contains("architecture") ?
                                     install_info["architecture"] : "64bit";

            // Step 2: Run pre-uninstall hook script
            if (manifest.is_valid()) {
                auto pre_uninstall_script = manifest.get_pre_uninstall(architecture);
                if (!pre_uninstall_script.empty()) {
                    if (!run_script(pre_uninstall_script, version_dir, "pre-uninstall")) {
                        output::warn("Pre-uninstall script failed for: {}", app_name);
                    }
                }
            }

            // Step 3: Check for running processes (Scoop's test_running_process)
            if (!options_.force && is_process_running(app_name)) {
                output::warn("Process is running for app: {}, skipping", app_name);
                return false;
            }

            // Step 4: Run uninstaller if present (Scoop's Invoke-Installer -Uninstall)
            if (manifest.is_valid()) {
                run_uninstaller(manifest, version_dir, architecture);
            }

            // Step 5: Remove shims (Scoop's rm_shims)
            if (!remove_app_shims(app_name, manifest, architecture)) {
                output::warn("Failed to remove some shims for: {}", app_name);
            }

            // Step 6: Remove start menu shortcuts (Scoop's rm_startmenu_shortcuts)
            remove_startmenu_shortcuts(manifest, architecture);

            // Step 7: Unlink current directory (Scoop's unlink_current)
            auto ref_dir = unlink_current_directory(version_dir);

            // Step 8: Uninstall PowerShell module (Scoop's uninstall_psmodule)
            uninstall_psmodule(manifest, ref_dir);

            // Step 9: Remove from PATH environment (Scoop's env_rm_path)
            env_remove_path(manifest, ref_dir, architecture);

            // Step 10: Remove environment variables (Scoop's env_rm)
            env_remove(manifest, architecture);

            // Step 11: Unlink persist data and remove version directory
            try {
                unlink_persist_data(manifest, version_dir);
                std::filesystem::remove_all(version_dir);
            } catch (const std::exception& e) {
                if (std::filesystem::exists(version_dir)) {
                    output::error("Couldn't remove '{}'; it may be in use.", version_dir.string());
                    return false;
                }
            }

            // Step 12: Run post-uninstall hook script
            if (manifest.is_valid()) {
                auto post_uninstall_script = manifest.get_post_uninstall(architecture);
                if (!post_uninstall_script.empty()) {
                    if (!run_script(post_uninstall_script, version_dir, "post-uninstall")) {
                        output::warn("Post-uninstall script failed for: {}", app_name);
                    }
                }
            }

            // Step 13: Remove older versions (Scoop behavior)
            remove_older_versions(app_name);

            // Step 14: Remove current symlink if exists
            auto current_dir = app_dir / "current";
            if (std::filesystem::exists(current_dir)) {
                try {
                    std::filesystem::remove_all(current_dir);
                } catch (const std::exception& e) {
                    output::warn("Failed to remove current directory: {}", e.what());
                }
            }

            // Step 15: Remove app directory if empty
            if (std::filesystem::exists(app_dir) && std::filesystem::is_empty(app_dir)) {
                try {
                    std::filesystem::remove(app_dir);
                } catch (const std::exception& e) {
                    if (std::filesystem::exists(app_dir)) {
                        output::warn("Failed to remove app directory: {}", e.what());
                    }
                }
            }

            // Step 16: Purge persistent data if requested
            if (options_.purge && std::filesystem::exists(persist_dir)) {
                std::cout << "Removing persisted data.\n";
                try {
                    std::filesystem::remove_all(persist_dir);
                } catch (const std::exception& e) {
                    output::error("Couldn't remove '{}'; it may be in use.", persist_dir.string());
                    return false;
                }
            }

            std::cout << "'" << app_name << "' was uninstalled.\n";
            return true;

        } catch (const std::exception& e) {
            output::error("Exception during uninstallation of {}: {}", app_name, e.what());
            return false;
        }
    }
    
    std::vector<std::string> determine_uninstall_order(const std::vector<std::string>& app_names) {
        // For uninstall, we want to remove apps in reverse dependency order
        // Apps that depend on others should be removed first
        
        std::vector<std::string> order;
        std::vector<std::string> remaining = app_names;
        
        while (!remaining.empty()) {
            bool found_removable = false;
            
            for (auto it = remaining.begin(); it != remaining.end(); ++it) {
                const auto& app_name = *it;
                
                // Check if any remaining app depends on this one
                bool has_dependent_in_remaining = false;
                for (const auto& other_app : remaining) {
                    if (other_app == app_name) continue;
                    
                    auto manifest = get_installed_manifest(other_app);
                    if (!manifest.is_valid()) continue;
                    
                    for (const auto& dep : manifest.depends) {
                        std::string dep_name = extract_app_name(dep);
                        if (dep_name == app_name) {
                            has_dependent_in_remaining = true;
                            break;
                        }
                    }
                    if (has_dependent_in_remaining) break;
                }
                
                if (!has_dependent_in_remaining) {
                    order.push_back(app_name);
                    remaining.erase(it);
                    found_removable = true;
                    break;
                }
            }
            
            if (!found_removable) {
                // Circular dependency or other issue, just add remaining apps
                order.insert(order.end(), remaining.begin(), remaining.end());
                break;
            }
        }
        
        return order;
    }
    
    bool remove_app_shims(const std::string& app_name, const Manifest& manifest) {
        bool success = true;

        if (manifest.is_valid()) {
            // Remove shims based on manifest bin entries
            auto bin_entries = manifest.get_bin();
            for (const auto& bin_entry : bin_entries) {
                std::string shim_name = extract_shim_name(bin_entry);

                // Show Scoop-style shim removal messages
                std::cout << "Removing shim '" << shim_name << ".shim'.\n";
                std::cout << "Removing shim '" << shim_name << ".exe'.\n";

                if (!ShimManager::remove_shim(shim_name)) {
                    std::cout << "⚠ Failed to remove shim: " << shim_name << "\n";
                    success = false;
                }
            }
        } else {
            // Fallback: remove all shims that point to this app
            if (!ShimManager::remove_shims_for_app(app_name)) {
                std::cout << "⚠ Failed to remove shims for app: " << app_name << "\n";
                success = false;
            }
        }

        return success;
    }

    // Overload with architecture parameter for Scoop compatibility
    bool remove_app_shims(const std::string& app_name, const Manifest& manifest, const std::string& architecture) {
        bool success = true;

        if (manifest.is_valid()) {
            // Remove shims based on manifest bin entries for specific architecture
            auto bin_entries = manifest.get_bin(architecture);
            for (const auto& bin_entry : bin_entries) {
                std::string shim_name = extract_shim_name(bin_entry);

                // Show Scoop-style shim removal messages
                std::cout << "Removing shim '" << shim_name << ".shim'.\n";
                std::cout << "Removing shim '" << shim_name << ".exe'.\n";

                if (!ShimManager::remove_shim(shim_name)) {
                    output::warn("Failed to remove shim: {}", shim_name);
                    success = false;
                }
            }
        } else {
            // Fallback: remove all shims that point to this app
            if (!ShimManager::remove_shims_for_app(app_name)) {
                output::warn("Failed to remove shims for app: {}", app_name);
                success = false;
            }
        }

        return success;
    }
    
    bool remove_registry_entries(const std::string& app_name, const Manifest& manifest) {
        // TODO: Implement registry cleanup
        // This would involve:
        // 1. Reading any registry entries created during installation
        // 2. Removing them safely
        // 3. Handling Windows-specific registry operations
        
        output::debug("Registry cleanup not yet implemented for: {}", app_name);
        return true;
    }
    
    bool remove_app_directory(const std::string& app_name) {
        try {
            auto app_dir = get_app_directory(app_name);

            if (!std::filesystem::exists(app_dir)) {
                output::debug("App directory already removed: {}", app_dir.string());
                return true;
            }

            // Show Scoop-style unlinking message
            auto current_dir = app_dir / "current";
            if (std::filesystem::exists(current_dir)) {
                std::cout << "Unlinking " << current_dir.string() << "\n";
            }

            // First, try to terminate any processes that might be using files in the directory
            try {
                // On Windows, try to use taskkill to terminate processes using files in the directory
                std::string cmd = "taskkill /F /FI \"MODULES eq " + app_dir.string() + "*\" 2>nul";
                std::system(cmd.c_str());
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            } catch (const std::exception& e) {
                output::debug("Failed to terminate processes: {}", e.what());
            }

            // Remove read-only attributes from all files and directories
            try {
                for (const auto& entry : std::filesystem::recursive_directory_iterator(app_dir,
                    std::filesystem::directory_options::skip_permission_denied)) {
                    try {
                        if (entry.is_regular_file() || entry.is_directory()) {
                            // Remove read-only attribute on Windows
                            auto perms = std::filesystem::status(entry.path()).permissions();
                            std::filesystem::permissions(entry.path(),
                                perms | std::filesystem::perms::owner_write,
                                std::filesystem::perm_options::replace);
                        }
                    } catch (const std::exception& e) {
                        output::debug("Failed to modify permissions for {}: {}", entry.path().string(), e.what());
                    }
                }
            } catch (const std::exception& e) {
                output::debug("Failed to iterate directory for permission changes: {}", e.what());
            }

            // Try multiple times with increasing delays for locked files
            int attempts = 5;
            for (int i = 0; i < attempts; ++i) {
                try {
                    // Try to remove individual files first, then directories
                    for (const auto& entry : std::filesystem::recursive_directory_iterator(app_dir,
                        std::filesystem::directory_options::skip_permission_denied)) {
                        try {
                            if (entry.is_regular_file()) {
                                std::filesystem::remove(entry.path());
                            }
                        } catch (const std::exception& e) {
                            output::debug("Failed to remove file {}: {}", entry.path().string(), e.what());
                        }
                    }

                    // Now try to remove the entire directory structure
                    std::filesystem::remove_all(app_dir);

                    if (!std::filesystem::exists(app_dir)) {
                        std::cout << "  Removed app directory: " << app_dir.string() << "\n";
                        return true;
                    }
                } catch (const std::exception& e) {
                    if (i == attempts - 1) {
                        // On final attempt, try using Windows rmdir command as fallback
                        try {
                            std::string cmd = "rmdir /s /q \"" + app_dir.string() + "\" 2>nul";
                            int result = std::system(cmd.c_str());
                            if (result == 0 && !std::filesystem::exists(app_dir)) {
                                std::cout << "  Removed app directory: " << app_dir.string() << "\n";
                                return true;
                            }
                        } catch (const std::exception& e2) {
                            output::debug("Fallback rmdir also failed: {}", e2.what());
                        }
                        throw; // Re-throw original exception
                    }
                    output::debug("Attempt {} failed, retrying: {}", i + 1, e.what());
                    std::this_thread::sleep_for(std::chrono::milliseconds(500 * (i + 1)));
                }
            }

            return false;

        } catch (const std::exception& e) {
            std::cout << "✗ Failed to remove app directory for " << app_name << ": " << e.what() << "\n";

            // As a last resort, try to rename the directory so it can be cleaned up later
            try {
                auto app_dir = get_app_directory(app_name);
                if (std::filesystem::exists(app_dir)) {
                    auto temp_name = app_dir.string() + ".sco_delete_" + std::to_string(std::time(nullptr));
                    std::filesystem::rename(app_dir, temp_name);
                    std::cout << "⚠ Directory renamed for later cleanup: " << temp_name << "\n";
                    std::cout << "  You may need to manually delete this directory later\n";
                    return true; // Consider this a success since the app is effectively uninstalled
                }
            } catch (const std::exception& e2) {
                output::debug("Failed to rename directory: {}", e2.what());
            }

            return false;
        }
    }
    
    bool run_script(const std::string& script, 
                   const std::filesystem::path& app_dir,
                   const std::string& script_type) {
        output::info("Running {} script", script_type);
        
        try {
            // Create temporary script file
            auto temp_script = app_dir / ("temp_" + script_type + ".ps1");
            
            std::ofstream script_file(temp_script);
            if (!script_file.is_open()) {
                output::error("Failed to create temporary script file");
                return false;
            }
            
            script_file << script;
            script_file.close();
            
            // Execute script
            std::string command = "powershell.exe -ExecutionPolicy Bypass -File \"" + 
                                temp_script.string() + "\"";
            
            output::debug("Executing script: {}", command);

            int exit_code = system(command.c_str());

            // Clean up temporary script
            std::filesystem::remove(temp_script);

            if (exit_code == 0) {
                output::info("{} script completed successfully", script_type);
                return true;
            } else {
                output::error("{} script failed with exit code: {}", script_type, exit_code);
                return false;
            }
            
        } catch (const std::exception& e) {
            output::error("Failed to run {} script: {}", script_type, e.what());
            return false;
        }
    }
    
public:
    // Helper methods
    static bool is_app_installed(const std::string& app_name) {
        return DependencyResolver::is_app_installed(app_name);
    }

    static std::vector<std::string> get_installed_apps() {
        std::vector<std::string> apps;
        
        auto& config = Config::instance();
        auto apps_dir = config.get_apps_dir();
        
        if (!std::filesystem::exists(apps_dir)) {
            return apps;
        }
        
        try {
            for (const auto& entry : std::filesystem::directory_iterator(apps_dir)) {
                if (entry.is_directory()) {
                    std::string app_name = entry.path().filename().string();
                    
                    // Check if there's a "current" directory or version directories
                    auto current_dir = entry.path() / "current";
                    if (std::filesystem::exists(current_dir)) {
                        apps.push_back(app_name);
                    } else {
                        // Check for version directories
                        bool has_version = false;
                        for (const auto& version_entry : std::filesystem::directory_iterator(entry.path())) {
                            if (version_entry.is_directory()) {
                                has_version = true;
                                break;
                            }
                        }
                        if (has_version) {
                            apps.push_back(app_name);
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            output::error("Failed to get installed apps: {}", e.what());
        }
        
        return apps;
    }

private:
    static Manifest get_installed_manifest(const std::string& app_name) {
        auto& config = Config::instance();

        // Check local installation first
        auto local_app_dir = config.get_apps_dir() / app_name;

        // Try to read from Scoop's manifest.json first (for compatibility)
        auto manifest_file = local_app_dir / "current" / "manifest.json";
        if (std::filesystem::exists(manifest_file)) {
            auto manifest = ManifestParser::parse_file(manifest_file);
            if (manifest.is_valid()) {
                return manifest;
            }
        }

        // Try to read bucket info from Scoop's install.json
        auto install_file = local_app_dir / "current" / "install.json";
        std::string bucket_name;
        if (std::filesystem::exists(install_file)) {
            try {
                std::ifstream file(install_file);
                nlohmann::json json;
                file >> json;
                if (json.contains("bucket")) {
                    bucket_name = json["bucket"].get<std::string>();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read Scoop install.json for {}: {}", app_name, e.what());
            }
        }

        // Fallback to SCO's scoop-install.json
        auto info_file = local_app_dir / "current" / "scoop-install.json";
        if (!std::filesystem::exists(info_file)) {
            info_file = local_app_dir / "scoop-install.json";
        }

        // If local installation info not found, check global
        if (!std::filesystem::exists(info_file) && bucket_name.empty()) {
            auto global_app_dir = config.get_global_apps_dir() / app_name;

            // Try Scoop's manifest.json in global
            manifest_file = global_app_dir / "current" / "manifest.json";
            if (std::filesystem::exists(manifest_file)) {
                auto manifest = ManifestParser::parse_file(manifest_file);
                if (manifest.is_valid()) {
                    return manifest;
                }
            }

            // Try Scoop's install.json in global
            install_file = global_app_dir / "current" / "install.json";
            if (std::filesystem::exists(install_file)) {
                try {
                    std::ifstream file(install_file);
                    nlohmann::json json;
                    file >> json;
                    if (json.contains("bucket")) {
                        bucket_name = json["bucket"].get<std::string>();
                    }
                } catch (const std::exception& e) {
                    output::debug("Failed to read global Scoop install.json for {}: {}", app_name, e.what());
                }
            }

            // Try SCO's scoop-install.json in global
            info_file = global_app_dir / "current" / "scoop-install.json";
            if (!std::filesystem::exists(info_file)) {
                info_file = global_app_dir / "scoop-install.json";
            }
        }

        // Try to read bucket from SCO's scoop-install.json if we don't have it yet
        if (bucket_name.empty() && std::filesystem::exists(info_file)) {
            try {
                std::ifstream file(info_file);
                nlohmann::json json;
                file >> json;

                if (json.contains("bucket")) {
                    bucket_name = json["bucket"].get<std::string>();
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read SCO installation info for {}: {}", app_name, e.what());
            }
        }

        // Search for manifest in buckets
        if (!bucket_name.empty()) {
            return ManifestParser::find_and_parse(app_name, bucket_name);
        }

        // Fallback: search in all buckets
        return ManifestParser::find_and_parse(app_name);
    }
    
    std::filesystem::path get_app_directory(const std::string& app_name) {
        // Check local installation first
        auto local_app_dir = config_.get_apps_dir() / app_name;
        if (std::filesystem::exists(local_app_dir)) {
            return local_app_dir;
        }

        // Check global installation if in global mode or if local doesn't exist
        if (options_.global) {
            auto global_app_dir = config_.get_global_apps_dir() / app_name;
            if (std::filesystem::exists(global_app_dir)) {
                return global_app_dir;
            }
        }

        // If not in global mode but local doesn't exist, still check global as fallback
        if (!options_.global) {
            auto global_app_dir = config_.get_global_apps_dir() / app_name;
            if (std::filesystem::exists(global_app_dir)) {
                return global_app_dir;
            }
        }

        // Return local path as default (even if it doesn't exist)
        return local_app_dir;
    }
    
    static std::string extract_app_name(const std::string& dependency) {
        // Handle different dependency formats:
        // - "app_name"
        // - "bucket/app_name"
        // - "app_name@version"
        // - "bucket/app_name@version"
        
        std::string result = dependency;
        
        // Remove version specifier if present
        size_t at_pos = result.find('@');
        if (at_pos != std::string::npos) {
            result = result.substr(0, at_pos);
        }
        
        // Extract app name from bucket/app format
        size_t slash_pos = result.find('/');
        if (slash_pos != std::string::npos) {
            result = result.substr(slash_pos + 1);
        }
        
        return result;
    }
    
    static std::string extract_shim_name(const std::string& bin_entry) {
        // Extract shim name from bin entry
        // For now, assume simple string format
        // TODO: Parse JSON array format properly
        return std::filesystem::path(bin_entry).stem().string();
    }
    
    template<typename Container>
    std::string join_strings(const Container& container, const std::string& delimiter) {
        if (container.empty()) return "";

        std::ostringstream oss;
        auto it = container.begin();
        oss << *it;
        ++it;

        for (; it != container.end(); ++it) {
            oss << delimiter << *it;
        }

        return oss.str();
    }

    std::filesystem::path get_persist_directory(const std::string& app_name) {
        auto persist_dir = options_.global ? config_.get_global_apps_dir().parent_path() / "persist"
                                          : config_.get_apps_dir().parent_path() / "persist";
        return persist_dir / app_name;
    }

    std::string get_current_version(const std::string& app_name) {
        // Scoop's Select-CurrentVersion function
        auto app_dir = get_app_directory(app_name);
        auto current_dir = app_dir / "current";

        if (std::filesystem::exists(current_dir)) {
            // If current is a symlink/junction, get the target
            try {
                if (std::filesystem::is_symlink(current_dir)) {
                    auto target = std::filesystem::read_symlink(current_dir);
                    return target.filename().string();
                } else {
                    // Check for version directories
                    for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                        if (entry.is_directory() && entry.path().filename() != "current") {
                            return entry.path().filename().string();
                        }
                    }
                }
            } catch (const std::exception& e) {
                output::debug("Failed to read current symlink: {}", e.what());
            }
        }

        // Fallback: find any version directory
        try {
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory() && entry.path().filename() != "current") {
                    return entry.path().filename().string();
                }
            }
        } catch (const std::exception& e) {
            output::debug("Failed to find version directory: {}", e.what());
        }

        return "";
    }

    nlohmann::json get_install_info(const std::string& app_name, const std::string& version) {
        // Scoop's install_info function
        auto app_dir = get_app_directory(app_name);
        auto version_dir = app_dir / version;
        auto install_file = version_dir / "install.json";

        if (std::filesystem::exists(install_file)) {
            try {
                std::ifstream file(install_file);
                nlohmann::json json;
                file >> json;
                return json;
            } catch (const std::exception& e) {
                output::debug("Failed to read install info: {}", e.what());
            }
        }

        return nlohmann::json{};
    }

    bool is_process_running(const std::string& app_name) {
        // Scoop's test_running_process function
        // TODO: Implement process detection
        return false;
    }

    void run_uninstaller(const Manifest& manifest, const std::filesystem::path& version_dir, const std::string& architecture) {
        // Scoop's Invoke-Installer -Uninstall
        // TODO: Add get_uninstaller method to Manifest class - this method doesn't exist yet
        // For now, skip uninstaller execution
        /*
        auto uninstaller = manifest.get_uninstaller(architecture);
        if (uninstaller.empty()) return;

        std::cout << "Running uninstaller...\n";
        // TODO: Implement uninstaller execution
        */
    }

    void remove_startmenu_shortcuts(const Manifest& manifest, const std::string& architecture) {
        // Scoop's rm_startmenu_shortcuts function
        auto shortcuts = manifest.get_shortcuts(architecture);
        if (shortcuts.empty()) return;

        for (const auto& shortcut : shortcuts) {
            if (shortcut.size() >= 2) {
                std::string shortcut_name = shortcut[1];
                std::cout << "Removing shortcut: " << shortcut_name << "\n";
                // TODO: Implement actual shortcut removal
            }
        }
    }

    std::filesystem::path unlink_current_directory(const std::filesystem::path& version_dir) {
        // Scoop's unlink_current function
        auto app_dir = version_dir.parent_path();
        auto current_dir = app_dir / "current";

        if (std::filesystem::exists(current_dir)) {
            try {
                std::filesystem::remove_all(current_dir);
            } catch (const std::exception& e) {
                output::warn("Failed to unlink current directory: {}", e.what());
            }
        }

        return version_dir; // Return the reference directory
    }

    void uninstall_psmodule(const Manifest& manifest, const std::filesystem::path& ref_dir) {
        // Scoop's uninstall_psmodule function
        // TODO: Add get_psmodule method to Manifest class - this method doesn't exist yet
        // For now, skip PowerShell module uninstallation
        /*
        auto psmodule = manifest.get_psmodule();
        if (psmodule.empty()) return;

        std::cout << "Uninstalling PowerShell module...\n";
        // TODO: Implement PowerShell module uninstallation
        */
    }

    void env_remove_path(const Manifest& manifest, const std::filesystem::path& ref_dir, const std::string& architecture) {
        // Scoop's env_rm_path function
        auto env_add_path = manifest.get_env_add_path(architecture);
        if (env_add_path.empty()) return;

        std::cout << "Removing from PATH environment variable...\n";
        // TODO: Implement PATH environment variable removal
    }

    void env_remove(const Manifest& manifest, const std::string& architecture) {
        // Scoop's env_rm function
        auto env_set = manifest.get_env_set(architecture);
        if (env_set.empty()) return;

        std::cout << "Removing environment variables...\n";
        // TODO: Implement environment variable removal
    }

    void unlink_persist_data(const Manifest& manifest, const std::filesystem::path& version_dir) {
        // Scoop's unlink_persist_data function
        auto persist_dirs = manifest.get_persist();
        if (persist_dirs.empty()) return;

        for (const auto& persist_dir : persist_dirs) {
            auto persist_path = version_dir / persist_dir;
            if (std::filesystem::exists(persist_path)) {
                try {
                    std::filesystem::remove_all(persist_path);
                } catch (const std::exception& e) {
                    output::warn("Failed to unlink persist data: {}", e.what());
                }
            }
        }
    }

    void remove_older_versions(const std::string& app_name) {
        // Scoop's behavior to remove older versions
        auto app_dir = get_app_directory(app_name);

        try {
            for (const auto& entry : std::filesystem::directory_iterator(app_dir)) {
                if (entry.is_directory() && entry.path().filename() != "current") {
                    std::string version = entry.path().filename().string();
                    std::cout << "Removing older version (" << version << ").\n";

                    try {
                        unlink_persist_data(Manifest{}, entry.path());
                        std::filesystem::remove_all(entry.path());
                    } catch (const std::exception& e) {
                        output::error("Couldn't remove '{}'; it may be in use.", entry.path().string());
                    }
                }
            }
        } catch (const std::exception& e) {
            output::debug("Failed to remove older versions: {}", e.what());
        }
    }
};

} // namespace sco
